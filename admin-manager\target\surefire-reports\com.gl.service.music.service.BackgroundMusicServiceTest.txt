-------------------------------------------------------------------------------
Test set: com.gl.service.music.service.BackgroundMusicServiceTest
-------------------------------------------------------------------------------
Tests run: 21, Failures: 4, Errors: 0, Skipped: 0, Time elapsed: 3.095 s <<< FAILURE! - in com.gl.service.music.service.BackgroundMusicServiceTest
testList_WithSearchCondition  Time elapsed: 0.068 s  <<< FAILURE!
org.mockito.exceptions.verification.opentest4j.ArgumentsAreDifferent: 

Argument(s) are different! Wanted:
jdbcTemplate.queryForObject(
    <any string>,
    class java.lang.Long,
    <any java.lang.Object[]>
);
-> at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:906)
Actual invocations have different arguments:
jdbcTemplate.queryForObject(
    "select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 and s.id = ?  and (m.name like ?)  AND s.id in (1)) t",
    class java.lang.Long,
    1L,
    "%测试音乐%"
);
-> at com.gl.service.music.service.BackgroundMusicService.list(BackgroundMusicService.java:88)

	at com.gl.service.music.service.BackgroundMusicServiceTest.testList_WithSearchCondition(BackgroundMusicServiceTest.java:248)

testList_EmptyResult  Time elapsed: 0.004 s  <<< FAILURE!
org.mockito.exceptions.verification.opentest4j.ArgumentsAreDifferent: 

Argument(s) are different! Wanted:
jdbcTemplate.queryForObject(
    <any string>,
    class java.lang.Long,
    <any java.lang.Object[]>
);
-> at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:906)
Actual invocations have different arguments:
jdbcTemplate.queryForObject(
    "select count(1) from (SELECT m.id,mt.name as musicTypeName,m.name,m.music_url,m.create_time,s.shop_name FROM dub_background_music m
LEFT JOIN dub_background_music_type mt
ON m.type_id = mt.id
LEFT JOIN dub_shop s ON s.id = m.shop_id 
WHERE m.del_status != 1 and s.id = ?  and (m.name like ?)  AND s.id in (1, 2)) t",
    class java.lang.Long,
    1L,
    "%测试音乐%"
);
-> at com.gl.service.music.service.BackgroundMusicService.list(BackgroundMusicService.java:88)

	at com.gl.service.music.service.BackgroundMusicServiceTest.testList_EmptyResult(BackgroundMusicServiceTest.java:219)

testList_NullDto  Time elapsed: 0.006 s  <<< FAILURE!
java.lang.AssertionError
	at com.gl.service.music.service.BackgroundMusicServiceTest.testList_NullDto(BackgroundMusicServiceTest.java:264)

testList_Success_WithData  Time elapsed: 0.003 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 总数应该为1 ==> expected: <1> but was: <0>
	at com.gl.service.music.service.BackgroundMusicServiceTest.testList_Success_WithData(BackgroundMusicServiceTest.java:160)

